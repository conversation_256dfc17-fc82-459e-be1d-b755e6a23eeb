#!/usr/bin/env python3
"""
Assets.car 文件修改工具
用于解压、修改和重新打包 iOS Assets.car 文件
"""

import os
import sys
import subprocess
import json
import shutil
from pathlib import Path

class AssetsCarModifier:
    def __init__(self, assets_car_path):
        self.assets_car_path = Path(assets_car_path)
        self.work_dir = Path("assets_work")
        self.extracted_dir = self.work_dir / "extracted"
        self.xcassets_dir = self.work_dir / "MyAssets.xcassets"
        
    def setup_work_directory(self):
        """创建工作目录"""
        if self.work_dir.exists():
            shutil.rmtree(self.work_dir)
        self.work_dir.mkdir()
        self.extracted_dir.mkdir()
        self.xcassets_dir.mkdir()
        
    def extract_assets_car(self):
        """解压 Assets.car 文件"""
        print("正在解压 Assets.car 文件...")
        
        # 尝试使用 acextract
        try:
            cmd = ["acextract", "-i", str(self.assets_car_path), "-o", str(self.extracted_dir)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("使用 acextract 解压成功")
                return True
        except FileNotFoundError:
            print("acextract 未找到，尝试使用 cartool...")
            
        # 尝试使用 cartool
        try:
            cmd = ["cartool", str(self.assets_car_path), str(self.extracted_dir)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("使用 cartool 解压成功")
                return True
        except FileNotFoundError:
            print("cartool 未找到")
            
        print("错误：未找到可用的解压工具 (acextract 或 cartool)")
        print("请安装其中一个工具：")
        print("- acextract: brew install acextract")
        print("- cartool: git clone https://github.com/steventroughtonsmith/cartool.git && cd cartool && make")
        return False
        
    def list_extracted_images(self):
        """列出解压的图片文件"""
        print("\n解压的图片文件：")
        image_files = []
        for ext in ['*.png', '*.jpg', '*.jpeg']:
            image_files.extend(self.extracted_dir.glob(f"**/{ext}"))
        
        for i, img_file in enumerate(image_files, 1):
            print(f"{i}. {img_file.name}")
            
        return image_files
        
    def create_xcassets_structure(self, image_files):
        """创建 .xcassets 目录结构"""
        print("\n创建 .xcassets 目录结构...")
        
        # 按图片名称分组（去除 @2x, @3x 后缀）
        image_groups = {}
        for img_file in image_files:
            base_name = img_file.stem
            # 移除 @2x, @3x 等后缀
            if '@' in base_name:
                base_name = base_name.split('@')[0]
            
            if base_name not in image_groups:
                image_groups[base_name] = []
            image_groups[base_name].append(img_file)
            
        # 为每个图片组创建 .imageset
        for group_name, files in image_groups.items():
            imageset_dir = self.xcassets_dir / f"{group_name}.imageset"
            imageset_dir.mkdir()
            
            # 创建 Contents.json
            contents = {
                "images": [],
                "info": {
                    "author": "xcode",
                    "version": 1
                }
            }
            
            # 复制图片文件并添加到 contents
            for img_file in files:
                # 确定缩放比例
                scale = "1x"
                if "@2x" in img_file.stem:
                    scale = "2x"
                elif "@3x" in img_file.stem:
                    scale = "3x"
                    
                # 复制文件
                dest_file = imageset_dir / img_file.name
                shutil.copy2(img_file, dest_file)
                
                # 添加到 contents
                contents["images"].append({
                    "filename": img_file.name,
                    "idiom": "universal",
                    "scale": scale
                })
                
            # 写入 Contents.json
            with open(imageset_dir / "Contents.json", 'w') as f:
                json.dump(contents, f, indent=2)
                
        print(f"创建了 {len(image_groups)} 个图片集")
        
    def repack_assets_car(self, output_path="Assets_modified.car"):
        """重新打包成 Assets.car"""
        print(f"\n重新打包成 {output_path}...")
        
        output_dir = self.work_dir / "output"
        output_dir.mkdir(exist_ok=True)
        
        cmd = [
            "xcrun", "actool",
            str(self.xcassets_dir),
            "--compile", str(output_dir),
            "--platform", "iphoneos",
            "--minimum-deployment-target", "9.0",
            "--output-partial-info-plist", str(output_dir / "partial.plist")
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                # 移动生成的 Assets.car 到指定位置
                generated_car = output_dir / "Assets.car"
                if generated_car.exists():
                    shutil.move(generated_car, output_path)
                    print(f"成功生成 {output_path}")
                    return True
                else:
                    print("错误：未找到生成的 Assets.car 文件")
            else:
                print(f"actool 执行失败：{result.stderr}")
        except Exception as e:
            print(f"重新打包失败：{e}")
            
        return False
        
    def modify_workflow(self):
        """完整的修改工作流程"""
        print("=== Assets.car 修改工具 ===\n")
        
        if not self.assets_car_path.exists():
            print(f"错误：文件不存在 {self.assets_car_path}")
            return False
            
        # 1. 设置工作目录
        self.setup_work_directory()
        
        # 2. 解压 Assets.car
        if not self.extract_assets_car():
            return False
            
        # 3. 列出图片文件
        image_files = self.list_extracted_images()
        if not image_files:
            print("未找到图片文件")
            return False
            
        # 4. 提示用户修改图片
        print(f"\n请在以下目录中修改你需要的图片：")
        print(f"{self.extracted_dir.absolute()}")
        print("\n修改完成后按 Enter 继续...")
        input()
        
        # 5. 创建 .xcassets 结构
        self.create_xcassets_structure(image_files)
        
        # 6. 重新打包
        return self.repack_assets_car()

def main():
    if len(sys.argv) != 2:
        print("用法: python assets_car_modifier.py <Assets.car文件路径>")
        sys.exit(1)
        
    assets_car_path = sys.argv[1]
    modifier = AssetsCarModifier(assets_car_path)
    
    if modifier.modify_workflow():
        print("\n✅ Assets.car 修改完成！")
    else:
        print("\n❌ Assets.car 修改失败")

if __name__ == "__main__":
    main()
