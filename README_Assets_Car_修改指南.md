# Assets.car 修改指南

## 概述
这个工具可以帮助你修改 iOS 应用中的 Assets.car 文件里的图片，并重新打包。

## 前置要求

### 1. 安装解压工具（选择其一）

#### 方法A：安装 acextract（推荐）
```bash
brew install acextract
```

#### 方法B：编译 cartool
```bash
git clone https://github.com/steventroughtonsmith/cartool.git
cd cartool
make
# 将编译好的 cartool 复制到 PATH 中
sudo cp cartool /usr/local/bin/
```

### 2. 确保有 Xcode 命令行工具
```bash
xcode-select --install
```

## 使用方法

### 自动化脚本方式（推荐）

1. **运行脚本**
```bash
python3 assets_car_modifier.py /path/to/Assets.car
```

2. **修改图片**
   - 脚本会解压 Assets.car 到 `assets_work/extracted/` 目录
   - 在该目录中找到你要修改的图片
   - 用你的新图片替换原图片（保持文件名不变）
   - 按 Enter 继续

3. **获取结果**
   - 脚本会自动重新打包生成 `Assets_modified.car`

### 手动方式

#### 第一步：解压 Assets.car
```bash
# 使用 acextract
acextract -i Assets.car -o extracted_images/

# 或使用 cartool
cartool Assets.car extracted_images/
```

#### 第二步：修改图片
在 `extracted_images/` 目录中找到并替换你需要的图片。

#### 第三步：创建 .xcassets 结构
```bash
mkdir MyAssets.xcassets
```

为每个图片创建对应的 `.imageset` 目录，例如：
```
MyAssets.xcassets/
├── icon.imageset/
│   ├── Contents.json
│   ├── icon.png
│   ├── <EMAIL>
│   └── <EMAIL>
└── background.imageset/
    ├── Contents.json
    └── background.png
```

每个 `Contents.json` 文件格式：
```json
{
  "images" : [
    {
      "filename" : "icon.png",
      "idiom" : "universal",
      "scale" : "1x"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "universal",
      "scale" : "2x"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "universal",
      "scale" : "3x"
    }
  ],
  "info" : {
    "author" : "xcode",
    "version" : 1
  }
}
```

#### 第四步：重新打包
```bash
xcrun actool MyAssets.xcassets \
    --compile output/ \
    --platform iphoneos \
    --minimum-deployment-target 9.0 \
    --output-partial-info-plist output/partial.plist
```

生成的 `Assets.car` 文件会在 `output/` 目录中。

## 注意事项

1. **保持文件名格式**：确保修改后的图片文件名与原文件完全一致
2. **保持分辨率规格**：@1x, @2x, @3x 的图片要保持正确的分辨率比例
3. **文件格式**：通常保持 PNG 格式
4. **备份原文件**：在修改前备份原始的 Assets.car 文件

## 常见问题

### Q: 提示找不到解压工具
A: 请先安装 acextract 或 cartool，参考上面的安装步骤。

### Q: actool 命令不存在
A: 请安装 Xcode 命令行工具：`xcode-select --install`

### Q: 重新打包后的文件无法使用
A: 检查 .xcassets 目录结构和 Contents.json 文件格式是否正确。

### Q: 图片显示异常
A: 确保替换的图片分辨率和原图片匹配，特别是 @2x 和 @3x 版本。

## 示例

假设你要修改一个名为 `Assets.car` 的文件：

```bash
# 1. 运行自动化脚本
python3 assets_car_modifier.py Assets.car

# 2. 在提示的目录中修改图片
# 3. 按 Enter 继续
# 4. 获得修改后的 Assets_modified.car 文件
```

完成后，用 `Assets_modified.car` 替换原应用中的 `Assets.car` 文件即可。
