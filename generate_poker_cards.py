#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成54张扑克牌的HTML页面
包含真实的扑克牌样式和布局
"""

def get_card_center_content(rank, symbol):
    """根据牌面值生成中心显示内容"""
    if rank in ['J', 'Q', 'K', 'A']:
        return f'<div class="single">{rank}</div>'

    # 数字牌显示对应数量的花色符号
    num = int(rank)
    if num <= 10:
        symbols = symbol * num
        if num <= 3:
            # 1-3个符号垂直排列
            return '<br>'.join([symbol] * num)
        elif num <= 6:
            # 4-6个符号分两列
            left_col = num // 2
            right_col = num - left_col
            content = '<div style="display: flex; justify-content: space-between; width: 100%; height: 100%;">'
            content += f'<div style="display: flex; flex-direction: column; justify-content: space-around;">{"<br>".join([symbol] * left_col)}</div>'
            content += f'<div style="display: flex; flex-direction: column; justify-content: space-around;">{"<br>".join([symbol] * right_col)}</div>'
            content += '</div>'
            return content
        else:
            # 7-10个符号分三列
            left_col = num // 3
            middle_col = num // 3
            right_col = num - left_col - middle_col
            content = '<div style="display: flex; justify-content: space-between; width: 100%; height: 100%;">'
            content += f'<div style="display: flex; flex-direction: column; justify-content: space-around;">{"<br>".join([symbol] * left_col)}</div>'
            content += f'<div style="display: flex; flex-direction: column; justify-content: space-around;">{"<br>".join([symbol] * middle_col)}</div>'
            content += f'<div style="display: flex; flex-direction: column; justify-content: space-around;">{"<br>".join([symbol] * right_col)}</div>'
            content += '</div>'
            return content

    return symbol

def generate_poker_html():
    """生成包含54张扑克牌的HTML页面"""

    # 定义花色和对应的符号
    suits = {
        'hearts': {'symbol': '♥', 'color': 'red', 'name': '红桃'},
        'diamonds': {'symbol': '♦', 'color': 'red', 'name': '方块'},
        'clubs': {'symbol': '♣', 'color': 'black', 'name': '梅花'},
        'spades': {'symbol': '♠', 'color': 'black', 'name': '黑桃'}
    }

    # 定义牌面值
    ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
    
    # 生成HTML头部
    html_content = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>54张扑克牌</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
            justify-items: center;
        }
        
        .card {
            width: 70px;
            height: 100px;
            background: white;
            border-radius: 8px;
            border: 2px solid #333;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 16px rgba(0,0,0,0.4);
        }
        
        .card.red {
            color: #d32f2f;
        }
        
        .card.black {
            color: #333;
        }
        
        .card-top {
            position: absolute;
            top: 3px;
            left: 3px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            font-size: 12px;
            font-weight: bold;
            line-height: 1;
        }

        .card-bottom {
            position: absolute;
            bottom: 3px;
            right: 3px;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            font-size: 12px;
            font-weight: bold;
            line-height: 1;
            transform: rotate(180deg);
        }
        
        .card-center {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-grow: 1;
            font-size: 16px;
            flex-wrap: wrap;
            padding: 2px;
        }

        .card-center.single {
            font-size: 24px;
        }

        .card-center.multiple {
            font-size: 12px;
            line-height: 1;
        }
        
        .joker {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        .joker.red-joker {
            background: linear-gradient(45deg, #ff4757, #ff3838);
        }
        
        .joker.black-joker {
            background: linear-gradient(45deg, #2f3542, #57606f);
        }
        
        .suit-display {
            margin-top: 20px;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            text-align: center;
        }
        
        .suit-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(5px);
        }
        
        .suit-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        @media (max-width: 768px) {
            .cards-grid {
                grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
                gap: 8px;
            }
            
            .card {
                width: 55px;
                height: 80px;
                padding: 3px;
            }
            
            .card-top, .card-bottom {
                font-size: 10px;
            }
            
            .card-center {
                font-size: 16px;
            }
            
            .suit-display {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🃏 54张扑克牌 🃏</h1>
        <div class="cards-grid">
"""
    
    # 生成52张普通牌
    for suit_info in suits.values():
        for rank in ranks:
            center_content = get_card_center_content(rank, suit_info['symbol'])
            center_class = "single" if rank in ['J', 'Q', 'K', 'A'] else "multiple"

            html_content += f"""
            <div class="card {suit_info['color']}" title="{suit_info['name']}{rank}">
                <div class="card-top">
                    <div>{rank}</div>
                    <div>{suit_info['symbol']}</div>
                </div>
                <div class="card-center {center_class}">
                    {center_content}
                </div>
                <div class="card-bottom">
                    <div>{rank}</div>
                    <div>{suit_info['symbol']}</div>
                </div>
            </div>"""
    
    # 生成2张王牌
    html_content += """
            <div class="card joker red-joker" title="小王">
                <div class="card-top">
                    <div>小</div>
                    <div>王</div>
                </div>
                <div class="card-center">
                    🃏
                </div>
                <div class="card-bottom">
                    <div>小</div>
                    <div>王</div>
                </div>
            </div>
            
            <div class="card joker black-joker" title="大王">
                <div class="card-top">
                    <div>大</div>
                    <div>王</div>
                </div>
                <div class="card-center">
                    🃏
                </div>
                <div class="card-bottom">
                    <div>大</div>
                    <div>王</div>
                </div>
            </div>
        </div>
        
        <div class="suit-display">
            <div class="suit-section">
                <div class="suit-title">♠ 黑桃</div>
                <div style="color: white;">13张牌</div>
            </div>
            <div class="suit-section">
                <div class="suit-title">♥ 红桃</div>
                <div style="color: white;">13张牌</div>
            </div>
            <div class="suit-section">
                <div class="suit-title">♣ 梅花</div>
                <div style="color: white;">13张牌</div>
            </div>
            <div class="suit-section">
                <div class="suit-title">♦ 方块</div>
                <div style="color: white;">13张牌</div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 20px; color: white;">
            <p>总计：52张普通牌 + 2张王牌 = 54张扑克牌</p>
            <p>鼠标悬停查看牌面详情</p>
        </div>
    </div>
    
    <script>
        // 添加交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    // 点击效果
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                    
                    // 显示牌面信息
                    const title = this.getAttribute('title');
                    if (title) {
                        console.log('点击了：' + title);
                    }
                });
            });
            
            // 统计信息
            console.log('扑克牌生成完成！');
            console.log('总计：' + cards.length + '张牌');
        });
    </script>
</body>
</html>"""
    
    return html_content

def main():
    """主函数"""
    print("正在生成54张扑克牌HTML页面...")
    
    # 生成HTML内容
    html_content = generate_poker_html()
    
    # 保存到文件
    with open('poker_cards.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ 扑克牌HTML页面生成完成！")
    print("📁 文件保存为：poker_cards.html")
    print("🌐 请在浏览器中打开查看效果")

if __name__ == "__main__":
    main()
